<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤数据导入工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007acc;
            background-color: #f8f9fa;
        }
        .command {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Consolas', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .file-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ 考勤数据导入工具</h1>
        
        <div class="step">
            <h3>📋 项目文件清单</h3>
            <div class="file-list">
                <p><strong>主要脚本：</strong></p>
                <ul>
                    <li>📄 <code>test_and_run.py</code> - 推荐使用的主脚本</li>
                    <li>📄 <code>smart_attendance_importer.py</code> - 智能导入器</li>
                    <li>📄 <code>simple_run.py</code> - 简化运行脚本</li>
                </ul>
                <p><strong>配置文件：</strong></p>
                <ul>
                    <li>⚙️ <code>config.py</code> - 数据库配置（已配置密码）</li>
                    <li>📋 <code>requirements.txt</code> - 依赖包列表</li>
                </ul>
                <p><strong>数据文件：</strong></p>
                <ul>
                    <li>📊 <code>8月.xlsx</code> - 考勤数据Excel文件</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>🚀 运行步骤</h3>
            
            <h4>步骤1: 安装依赖包</h4>
            <div class="command">pip install pandas mysql-connector-python openpyxl xlrd</div>
            
            <h4>步骤2: 运行程序（选择其中一种方式）</h4>
            
            <p><strong>方式1: 完整测试和导入（推荐）</strong></p>
            <div class="command">python test_and_run.py</div>
            
            <p><strong>方式2: 智能导入器</strong></p>
            <div class="command">python smart_attendance_importer.py</div>
            
            <p><strong>方式3: 简化运行</strong></p>
            <div class="command">python simple_run.py</div>
            
            <p><strong>方式4: Windows批处理</strong></p>
            <div class="command">run.bat</div>
        </div>

        <div class="step">
            <h3>📊 数据库表结构</h3>
            <p>程序会自动创建以下表：</p>
            <div class="command">
CREATE TABLE attendance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    date DATE NOT NULL COMMENT '日期',
    attendance_type VARCHAR(50) NOT NULL COMMENT '考勤类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_employee_date (employee_name, date)
);
            </div>
        </div>

        <div class="note">
            <h4>💡 重要提示</h4>
            <ul>
                <li>数据库配置已设置，密码已配置为 <code>6547899x</code></li>
                <li>程序会自动创建数据库表</li>
                <li>重复数据会自动更新，不会重复插入</li>
                <li>所有操作都有详细日志记录</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔧 故障排除</h3>
            <p><strong>如果遇到问题：</strong></p>
            <ol>
                <li>确保MySQL服务已启动</li>
                <li>确认数据库 <code>attendance_db</code> 已创建</li>
                <li>检查网络连接</li>
                <li>查看生成的日志文件</li>
            </ol>
        </div>

        <div class="step">
            <h3>📝 手动执行SQL</h3>
            <p>如果需要手动创建数据库：</p>
            <div class="command">
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE attendance_db;
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <p><strong>🎯 现在可以在命令行中运行上述任一命令来导入考勤数据！</strong></p>
        </div>
    </div>
</body>
</html>
