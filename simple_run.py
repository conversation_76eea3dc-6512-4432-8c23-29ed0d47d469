#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化运行脚本
"""

import sys
import os

print("开始运行考勤数据导入程序...")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")
print(f"文件列表: {os.listdir('.')}")

# 检查Excel文件
if os.path.exists('8月.xlsx'):
    print("✓ 找到Excel文件: 8月.xlsx")
else:
    print("✗ 未找到Excel文件: 8月.xlsx")
    sys.exit(1)

# 尝试导入必要的库
try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")
    sys.exit(1)

try:
    import mysql.connector
    print("✓ mysql.connector 导入成功")
except ImportError as e:
    print(f"✗ mysql.connector 导入失败: {e}")
    sys.exit(1)

# 读取Excel文件
try:
    print("正在读取Excel文件...")
    df = pd.read_excel('8月.xlsx')
    print(f"✓ Excel文件读取成功，数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print("前3行数据:")
    print(df.head(3))
except Exception as e:
    print(f"✗ Excel文件读取失败: {e}")
    sys.exit(1)

# 测试数据库连接
try:
    from config import DATABASE_CONFIG
    print("✓ 配置文件读取成功")
    
    print("正在测试数据库连接...")
    connection = mysql.connector.connect(**DATABASE_CONFIG)
    if connection.is_connected():
        print("✓ 数据库连接成功")
        connection.close()
    else:
        print("✗ 数据库连接失败")
        sys.exit(1)
        
except Exception as e:
    print(f"✗ 数据库连接测试失败: {e}")
    sys.exit(1)

# 如果所有测试都通过，运行智能导入器
try:
    print("\n开始执行数据导入...")
    from smart_attendance_importer import main as import_main
    import_main()
    print("✓ 数据导入完成")
except Exception as e:
    print(f"✗ 数据导入失败: {e}")
    import traceback
    traceback.print_exc()

print("程序执行完毕")
