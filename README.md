# 考勤数据导入工具

这个工具用于将Excel格式的考勤数据导入到MySQL数据库中。

## 文件说明

- `8月.xlsx` - 考勤数据Excel文件
- `attendance_to_mysql.py` - 主要的数据导入脚本
- `check_excel.py` - Excel文件结构分析工具
- `config.py` - 配置文件
- `requirements.txt` - Python依赖包列表

## 安装步骤

### 1. 安装Python依赖包

```bash
pip install -r requirements.txt
```

或者单独安装：

```bash
pip install pandas mysql-connector-python openpyxl xlrd
```

### 2. 配置数据库

在 `config.py` 文件中修改数据库连接信息：

```python
DATABASE_CONFIG = {
    'host': 'localhost',          # 数据库主机地址
    'port': 3306,                 # 数据库端口
    'database': 'attendance_db',  # 数据库名称
    'user': 'your_username',      # 数据库用户名
    'password': 'your_password',  # 数据库密码
    'charset': 'utf8mb4',
    'use_unicode': True,
    'autocommit': False
}
```

### 3. 创建数据库

在MySQL中创建数据库：

```sql
CREATE DATABASE attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 使用方法

### 方法一：一键运行（推荐）

运行完整的测试和导入流程：

```bash
python test_and_run.py
```

这个脚本会自动：
1. 检查Python依赖包
2. 分析Excel文件结构
3. 测试数据库连接
4. 预览要导入的数据
5. 确认后执行导入

### 方法二：分步执行

#### 第一步：分析Excel文件结构

```bash
python check_excel.py
```

#### 第二步：使用智能导入器

```bash
python smart_attendance_importer.py
```

#### 第三步：或使用基础导入器

```bash
python attendance_to_mysql.py
```

### 方法三：Windows批处理

双击运行 `run.bat` 文件，按提示操作。

## 数据库表结构

程序会自动创建以下表结构：

```sql
CREATE TABLE attendance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    date DATE NOT NULL COMMENT '日期',
    attendance_type VARCHAR(50) NOT NULL COMMENT '考勤类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_employee_date (employee_name, date),
    INDEX idx_date (date),
    INDEX idx_attendance_type (attendance_type)
);
```

## 常见问题

### 1. 连接数据库失败
- 检查数据库服务是否启动
- 确认用户名和密码是否正确
- 确认数据库是否存在

### 2. Excel文件读取失败
- 确认文件路径是否正确
- 确认文件没有被其他程序占用
- 检查文件格式是否正确

### 3. 数据格式问题
- 运行 `check_excel.py` 分析数据结构
- 根据实际数据格式调整 `attendance_to_mysql.py` 中的处理逻辑

## 日志文件

程序运行时会生成 `attendance_import.log` 日志文件，记录详细的执行过程和错误信息。

## 注意事项

1. 确保Excel文件格式正确，第一列为员工姓名，其他列为日期
2. 日期格式应该是标准格式
3. 考勤类型数据应该是文本格式
4. 程序支持重复运行，相同的记录会被更新而不是重复插入
