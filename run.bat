@echo off
chcp 65001 >nul
echo ========================================
echo 考勤数据导入工具
echo ========================================
echo.

echo 1. 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)
echo Python环境检查通过

echo.
echo 2. 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 警告: 依赖包安装可能有问题，但继续执行...
)

echo.
echo 3. 分析Excel文件结构...
python check_excel.py
if errorlevel 1 (
    echo 错误: Excel文件分析失败
    pause
    exit /b 1
)

echo.
echo 4. 是否继续导入数据到MySQL? (y/n)
set /p choice=请输入选择: 
if /i "%choice%"=="y" (
    echo 开始导入数据...
    python attendance_to_mysql.py
    if errorlevel 1 (
        echo 数据导入过程中出现错误，请查看日志文件
    ) else (
        echo 数据导入完成！
    )
) else (
    echo 已取消数据导入
)

echo.
echo 按任意键退出...
pause >nul
