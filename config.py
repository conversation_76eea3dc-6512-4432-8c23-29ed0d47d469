#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含数据库连接配置和其他设置
"""

# MySQL数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',          # 数据库主机地址
    'port': 3306,                 # 数据库端口
    'database': 'attendance_db',  # 数据库名称
    'user': 'root',               # 数据库用户名
    'password': '6547899x',  # 数据库密码
    'charset': 'utf8mb4',
    'use_unicode': True,
    'autocommit': False
}

# Excel文件配置
EXCEL_CONFIG = {
    'file_path': '8月.xlsx',      # Excel文件路径
    'sheet_name': 0,              # 工作表索引或名称（0表示第一个工作表）
    'header_row': 0,              # 表头行索引
    'name_column': 0,             # 姓名列索引
    'date_start_column': 1        # 日期列开始索引
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'file': 'attendance_import.log',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'encoding': 'utf-8'
}

# 数据处理配置
DATA_CONFIG = {
    'default_year': 2024,         # 默认年份
    'default_month': 8,           # 默认月份
    'skip_empty_cells': True,     # 是否跳过空单元格
    'trim_whitespace': True       # 是否去除前后空格
}
