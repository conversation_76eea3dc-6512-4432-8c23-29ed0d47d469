#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤数据从Excel导入到MySQL的脚本
处理8月.xlsx文件中的考勤数据并保存到MySQL数据库
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('attendance_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AttendanceImporter:
    def __init__(self, db_config):
        """
        初始化考勤数据导入器
        
        Args:
            db_config (dict): MySQL数据库连接配置
        """
        self.db_config = db_config
        self.connection = None
        
    def connect_to_database(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def create_attendance_table(self):
        """创建考勤数据表"""
        create_table_query = """
        CREATE TABLE IF NOT EXISTS attendance_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
            date DATE NOT NULL COMMENT '日期',
            attendance_type VARCHAR(50) NOT NULL COMMENT '考勤类型',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_employee_date (employee_name, date),
            INDEX idx_date (date),
            INDEX idx_attendance_type (attendance_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考勤记录表';
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(create_table_query)
            self.connection.commit()
            logger.info("考勤数据表创建成功")
            cursor.close()
            return True
        except Error as e:
            logger.error(f"创建数据表失败: {e}")
            return False
    
    def read_excel_data(self, excel_file):
        """
        读取Excel文件数据
        
        Args:
            excel_file (str): Excel文件路径
            
        Returns:
            pandas.DataFrame: 处理后的数据
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(excel_file):
                logger.error(f"Excel文件不存在: {excel_file}")
                return None
            
            # 读取Excel文件
            logger.info(f"开始读取Excel文件: {excel_file}")
            
            # 尝试读取所有工作表
            excel_file_obj = pd.ExcelFile(excel_file)
            sheet_names = excel_file_obj.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 读取第一个工作表（通常是主要数据）
            df = pd.read_excel(excel_file, sheet_name=0)
            logger.info(f"数据形状: {df.shape}")
            logger.info(f"列名: {df.columns.tolist()}")
            
            # 显示前几行数据以便分析结构
            logger.info("前5行数据:")
            logger.info(df.head().to_string())
            
            return df
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return None
    
    def process_attendance_data(self, df):
        """
        处理考勤数据，转换为标准格式
        
        Args:
            df (pandas.DataFrame): 原始数据
            
        Returns:
            list: 处理后的数据记录列表
        """
        records = []
        
        try:
            # 这里需要根据实际的Excel结构来调整
            # 假设Excel的结构是：第一列是姓名，其他列是日期，单元格内容是考勤类型
            
            # 获取列名（日期）
            date_columns = df.columns[1:]  # 假设第一列是姓名，其他列是日期
            
            for index, row in df.iterrows():
                employee_name = str(row.iloc[0]).strip()  # 第一列是员工姓名
                
                # 跳过空行或无效行
                if pd.isna(employee_name) or employee_name == '' or employee_name == 'nan':
                    continue
                
                # 处理每一天的考勤数据
                for date_col in date_columns:
                    attendance_type = str(row[date_col]).strip()
                    
                    # 跳过空值
                    if pd.isna(row[date_col]) or attendance_type == '' or attendance_type == 'nan':
                        continue
                    
                    # 尝试解析日期
                    try:
                        # 如果列名就是日期
                        if isinstance(date_col, str):
                            # 尝试多种日期格式
                            date_str = date_col
                            for date_format in ['%Y-%m-%d', '%m-%d', '%d', '%Y/%m/%d', '%m/%d']:
                                try:
                                    if date_format in ['%m-%d', '%d']:
                                        # 补充年份（假设是2024年8月）
                                        if date_format == '%d':
                                            date_str = f"2024-08-{date_col}"
                                        else:
                                            date_str = f"2024-{date_col}"
                                        parsed_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                                    else:
                                        parsed_date = datetime.strptime(date_str, date_format).date()
                                    break
                                except ValueError:
                                    continue
                            else:
                                logger.warning(f"无法解析日期: {date_col}")
                                continue
                        else:
                            parsed_date = date_col.date() if hasattr(date_col, 'date') else date_col
                    
                    except Exception as e:
                        logger.warning(f"日期解析失败 {date_col}: {e}")
                        continue
                    
                    # 添加记录
                    records.append({
                        'employee_name': employee_name,
                        'date': parsed_date,
                        'attendance_type': attendance_type
                    })
            
            logger.info(f"处理完成，共生成 {len(records)} 条记录")
            return records
            
        except Exception as e:
            logger.error(f"处理数据失败: {e}")
            return []
    
    def insert_data_to_mysql(self, records):
        """
        将数据插入到MySQL数据库
        
        Args:
            records (list): 要插入的记录列表
        """
        if not records:
            logger.warning("没有数据需要插入")
            return False
        
        insert_query = """
        INSERT INTO attendance_records (employee_name, date, attendance_type)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE
        attendance_type = VALUES(attendance_type),
        updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            cursor = self.connection.cursor()
            
            # 批量插入数据
            data_tuples = [(record['employee_name'], record['date'], record['attendance_type']) 
                          for record in records]
            
            cursor.executemany(insert_query, data_tuples)
            self.connection.commit()
            
            logger.info(f"成功插入 {cursor.rowcount} 条记录到数据库")
            cursor.close()
            return True
            
        except Error as e:
            logger.error(f"插入数据失败: {e}")
            self.connection.rollback()
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    # 导入配置
    try:
        from config import DATABASE_CONFIG, EXCEL_CONFIG
        db_config = DATABASE_CONFIG
        excel_file = EXCEL_CONFIG['file_path']
    except ImportError:
        logger.warning("未找到config.py文件，使用默认配置")
        # MySQL数据库配置
        db_config = {
            'host': 'localhost',
            'database': 'attendance_db',
            'user': 'root',
            'password': 'your_password',
            'charset': 'utf8mb4',
            'use_unicode': True
        }
        excel_file = '8月.xlsx'

    
    # 创建导入器实例
    importer = AttendanceImporter(db_config)
    
    try:
        # 连接数据库
        if not importer.connect_to_database():
            logger.error("无法连接到数据库，程序退出")
            return
        
        # 创建数据表
        if not importer.create_attendance_table():
            logger.error("无法创建数据表，程序退出")
            return
        
        # 读取Excel数据
        df = importer.read_excel_data(excel_file)
        if df is None:
            logger.error("无法读取Excel文件，程序退出")
            return
        
        # 处理数据
        records = importer.process_attendance_data(df)
        if not records:
            logger.error("没有有效的数据记录，程序退出")
            return
        
        # 插入数据到MySQL
        if importer.insert_data_to_mysql(records):
            logger.info("考勤数据导入完成！")
        else:
            logger.error("数据导入失败")
    
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    
    finally:
        # 关闭数据库连接
        importer.close_connection()

if __name__ == "__main__":
    main()
