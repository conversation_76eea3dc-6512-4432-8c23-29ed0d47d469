# 考勤数据导入工具使用说明

## 概述

这个工具可以将Excel格式的考勤数据自动导入到MySQL数据库中。支持多种Excel格式，能够智能识别数据结构。

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `8月.xlsx` | 考勤数据Excel文件 |
| `test_and_run.py` | **主要运行脚本**（推荐使用） |
| `smart_attendance_importer.py` | 智能导入器（自动识别Excel格式） |
| `attendance_to_mysql.py` | 基础导入器 |
| `check_excel.py` | Excel文件结构分析工具 |
| `config.py` | 配置文件 |
| `requirements.txt` | Python依赖包列表 |
| `run.bat` | Windows批处理文件 |

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

编辑 `config.py` 文件，修改数据库连接信息：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'database': 'attendance_db',
    'user': 'your_username',      # 修改为你的用户名
    'password': 'your_password',  # 修改为你的密码
    # 其他配置保持不变
}
```

### 3. 创建数据库

在MySQL中执行：

```sql
CREATE DATABASE attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 运行导入

**推荐方式：**

```bash
python test_and_run.py
```

这个脚本会：
- ✅ 自动检查所有依赖
- ✅ 分析Excel文件结构
- ✅ 测试数据库连接
- ✅ 预览要导入的数据
- ✅ 确认后执行导入

## Excel文件格式要求

工具支持以下Excel格式：

### 标准格式
```
| 姓名   | 1  | 2  | 3  | ... | 31 |
|--------|----|----|----|----|----| 
| 张三   | 正常| 迟到| 正常| ... | 休假|
| 李四   | 正常| 正常| 病假| ... | 正常|
```

### 支持的日期格式
- 纯数字：`1`, `2`, `31`
- 带日字：`1日`, `2日`, `31日`
- 完整日期：`2024-08-01`, `8/1`, `8-1`

### 支持的考勤类型
- 正常、迟到、早退、病假、事假、年假、休假等
- 任何文本都可以作为考勤类型

## 数据库表结构

程序会自动创建以下表：

```sql
CREATE TABLE attendance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    date DATE NOT NULL COMMENT '日期',
    attendance_type VARCHAR(50) NOT NULL COMMENT '考勤类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_employee_date (employee_name, date)
);
```

## 功能特点

### 🔍 智能识别
- 自动检测Excel文件结构
- 智能识别姓名列和日期列
- 支持多种日期格式

### 🛡️ 数据安全
- 重复数据自动更新，不会重复插入
- 完整的错误处理和日志记录
- 数据库事务保护

### 📊 数据预览
- 导入前可预览数据
- 显示将要导入的记录数量
- 支持数据验证

### 🔧 灵活配置
- 支持配置文件
- 可自定义数据库连接
- 可调整数据处理参数

## 故障排除

### 1. 依赖包安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 确认用户名密码是否正确
- 确认数据库是否存在
- 检查防火墙设置

### 3. Excel文件读取失败
- 确认文件路径正确
- 确认文件没有被其他程序占用
- 检查文件格式是否为.xlsx或.xls

### 4. 数据格式问题
- 运行 `python check_excel.py` 分析数据结构
- 检查姓名列是否包含有效数据
- 确认日期列格式是否正确

## 日志文件

程序运行时会生成日志文件：
- `attendance_import.log` - 基础导入器日志
- `smart_import.log` - 智能导入器日志

查看日志可以了解详细的执行过程和错误信息。

## 联系支持

如果遇到问题，请：
1. 查看日志文件中的错误信息
2. 运行 `python test_and_run.py` 进行完整测试
3. 检查Excel文件和数据库配置是否正确

## 版本信息

- 版本：1.0
- 支持Python：3.7+
- 支持MySQL：5.7+
- 支持Excel格式：.xlsx, .xls
