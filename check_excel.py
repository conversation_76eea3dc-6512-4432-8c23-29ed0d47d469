#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构的脚本
用于分析8月.xlsx文件的数据格式
"""

import pandas as pd
import sys
import os

def check_excel_structure(excel_file):
    """检查Excel文件结构"""
    try:
        # 检查文件是否存在
        if not os.path.exists(excel_file):
            print(f"错误: Excel文件不存在: {excel_file}")
            return False
        
        print(f"正在分析Excel文件: {excel_file}")
        print("=" * 50)
        
        # 读取Excel文件信息
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")
        print()
        
        # 分析每个工作表
        for i, sheet_name in enumerate(sheet_names):
            print(f"工作表 {i+1}: {sheet_name}")
            print("-" * 30)
            
            try:
                # 读取工作表数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
                print(f"列名: {df.columns.tolist()}")
                print()
                
                # 显示前几行数据
                print("前5行数据:")
                print(df.head().to_string())
                print()
                
                # 显示数据类型
                print("数据类型:")
                print(df.dtypes.to_string())
                print()
                
                # 检查空值
                print("空值统计:")
                null_counts = df.isnull().sum()
                print(null_counts[null_counts > 0].to_string())
                print()
                
                # 如果是考勤数据，尝试分析结构
                if df.shape[1] > 1:
                    print("数据结构分析:")
                    print(f"第一列 (可能是姓名): {df.iloc[:, 0].name}")
                    print(f"第一列前5个值: {df.iloc[:5, 0].tolist()}")
                    print()
                    
                    if df.shape[1] > 1:
                        print(f"其他列 (可能是日期): {df.columns[1:].tolist()}")
                        print("第二列前5个值:", df.iloc[:5, 1].tolist())
                        print()
                
            except Exception as e:
                print(f"读取工作表 {sheet_name} 时出错: {e}")
            
            print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return False

def main():
    """主函数"""
    excel_file = '8月.xlsx'
    
    print("Excel文件结构分析工具")
    print("=" * 50)
    
    if check_excel_structure(excel_file):
        print("分析完成！")
        print("\n请根据上述信息调整 attendance_to_mysql.py 中的数据处理逻辑。")
    else:
        print("分析失败！")

if __name__ == "__main__":
    main()
