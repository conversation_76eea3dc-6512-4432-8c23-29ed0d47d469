#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能考勤数据导入器
自动检测Excel文件格式并导入到MySQL数据库
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import os
import re
from datetime import datetime, date
import calendar

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SmartAttendanceImporter:
    def __init__(self, db_config):
        self.db_config = db_config
        self.connection = None
        
    def connect_to_database(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def create_attendance_table(self):
        """创建考勤数据表"""
        create_table_query = """
        CREATE TABLE IF NOT EXISTS attendance_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
            date DATE NOT NULL COMMENT '日期',
            attendance_type VARCHAR(50) NOT NULL COMMENT '考勤类型',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_employee_date (employee_name, date),
            INDEX idx_date (date),
            INDEX idx_attendance_type (attendance_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考勤记录表';
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(create_table_query)
            self.connection.commit()
            logger.info("考勤数据表创建成功")
            cursor.close()
            return True
        except Error as e:
            logger.error(f"创建数据表失败: {e}")
            return False
    
    def analyze_excel_structure(self, excel_file):
        """智能分析Excel文件结构"""
        try:
            # 读取Excel文件
            excel_obj = pd.ExcelFile(excel_file)
            sheet_names = excel_obj.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 分析第一个工作表
            df = pd.read_excel(excel_file, sheet_name=0)
            logger.info(f"数据形状: {df.shape}")
            
            # 智能检测结构
            structure = self._detect_structure(df)
            logger.info(f"检测到的结构: {structure}")
            
            return df, structure
            
        except Exception as e:
            logger.error(f"分析Excel文件失败: {e}")
            return None, None
    
    def _detect_structure(self, df):
        """检测Excel数据结构"""
        structure = {
            'name_column': 0,
            'date_columns': [],
            'data_start_row': 0,
            'year': 2024,
            'month': 8
        }
        
        # 检测姓名列（通常是第一列，包含中文姓名）
        first_col = df.iloc[:, 0].dropna()
        if len(first_col) > 0:
            # 检查是否包含中文字符
            chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
            chinese_count = sum(1 for name in first_col.astype(str) if chinese_pattern.search(name))
            if chinese_count > len(first_col) * 0.5:  # 超过50%包含中文
                structure['name_column'] = 0
                logger.info("检测到姓名列在第一列")
        
        # 检测日期列
        for i, col in enumerate(df.columns[1:], 1):
            col_str = str(col)
            # 检测各种日期格式
            if self._is_date_column(col_str):
                structure['date_columns'].append(i)
        
        # 如果没有检测到明确的日期列，假设除第一列外都是日期列
        if not structure['date_columns']:
            structure['date_columns'] = list(range(1, len(df.columns)))
            logger.info("未检测到明确日期格式，假设除第一列外都是日期列")
        
        # 从文件名或列名推断年月
        year, month = self._extract_year_month(df.columns)
        if year:
            structure['year'] = year
        if month:
            structure['month'] = month
            
        return structure
    
    def _is_date_column(self, col_str):
        """判断列名是否为日期格式"""
        date_patterns = [
            r'^\d{1,2}$',           # 1, 2, 31 (日)
            r'^\d{1,2}日$',         # 1日, 31日
            r'^\d{4}-\d{1,2}-\d{1,2}$',  # 2024-08-01
            r'^\d{1,2}/\d{1,2}$',   # 8/1, 08/01
            r'^\d{1,2}-\d{1,2}$',   # 8-1, 08-01
        ]
        
        for pattern in date_patterns:
            if re.match(pattern, col_str):
                return True
        return False
    
    def _extract_year_month(self, columns):
        """从列名中提取年月信息"""
        year, month = None, None
        
        # 从列名中查找年月信息
        for col in columns:
            col_str = str(col)
            
            # 匹配 2024-08-01 格式
            match = re.search(r'(\d{4})-(\d{1,2})', col_str)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                break
                
            # 匹配 8月 格式
            match = re.search(r'(\d{1,2})月', col_str)
            if match:
                month = int(match.group(1))
        
        return year, month
    
    def process_data_smart(self, df, structure):
        """智能处理数据"""
        records = []
        name_col = structure['name_column']
        date_cols = structure['date_columns']
        year = structure['year']
        month = structure['month']
        
        logger.info(f"处理数据: 姓名列={name_col}, 日期列={date_cols}, 年月={year}-{month}")
        
        for index, row in df.iterrows():
            employee_name = str(row.iloc[name_col]).strip()
            
            # 跳过无效姓名
            if pd.isna(row.iloc[name_col]) or employee_name in ['', 'nan', 'NaN']:
                continue
            
            # 跳过表头行
            if '姓名' in employee_name or '员工' in employee_name:
                continue
            
            # 处理每个日期列
            for date_col_idx in date_cols:
                if date_col_idx >= len(row):
                    continue
                    
                attendance_type = str(row.iloc[date_col_idx]).strip()
                
                # 跳过空值
                if pd.isna(row.iloc[date_col_idx]) or attendance_type in ['', 'nan', 'NaN']:
                    continue
                
                # 解析日期
                col_name = df.columns[date_col_idx]
                parsed_date = self._parse_date(col_name, year, month)
                
                if parsed_date:
                    records.append({
                        'employee_name': employee_name,
                        'date': parsed_date,
                        'attendance_type': attendance_type
                    })
        
        logger.info(f"智能处理完成，生成 {len(records)} 条记录")
        return records
    
    def _parse_date(self, col_name, year, month):
        """解析日期"""
        col_str = str(col_name)
        
        try:
            # 尝试各种日期格式
            
            # 格式1: 纯数字 (日)
            if re.match(r'^\d{1,2}$', col_str):
                day = int(col_str)
                return date(year, month, day)
            
            # 格式2: 数字+日
            match = re.match(r'^(\d{1,2})日$', col_str)
            if match:
                day = int(match.group(1))
                return date(year, month, day)
            
            # 格式3: YYYY-MM-DD
            match = re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', col_str)
            if match:
                y, m, d = int(match.group(1)), int(match.group(2)), int(match.group(3))
                return date(y, m, d)
            
            # 格式4: MM/DD 或 M/D
            match = re.match(r'^(\d{1,2})/(\d{1,2})$', col_str)
            if match:
                m, d = int(match.group(1)), int(match.group(2))
                return date(year, m, d)
            
            # 格式5: MM-DD 或 M-D
            match = re.match(r'^(\d{1,2})-(\d{1,2})$', col_str)
            if match:
                m, d = int(match.group(1)), int(match.group(2))
                return date(year, m, d)
                
        except ValueError as e:
            logger.warning(f"日期解析失败 {col_str}: {e}")
        
        return None
    
    def insert_data_batch(self, records):
        """批量插入数据"""
        if not records:
            logger.warning("没有数据需要插入")
            return False
        
        insert_query = """
        INSERT INTO attendance_records (employee_name, date, attendance_type)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE
        attendance_type = VALUES(attendance_type),
        updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            cursor = self.connection.cursor()
            
            # 准备数据
            data_tuples = [(record['employee_name'], record['date'], record['attendance_type']) 
                          for record in records]
            
            # 批量插入
            cursor.executemany(insert_query, data_tuples)
            self.connection.commit()
            
            logger.info(f"成功插入/更新 {cursor.rowcount} 条记录")
            cursor.close()
            return True
            
        except Error as e:
            logger.error(f"插入数据失败: {e}")
            self.connection.rollback()
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    # 数据库配置
    try:
        from config import DATABASE_CONFIG
        db_config = DATABASE_CONFIG
    except ImportError:
        db_config = {
            'host': 'localhost',
            'database': 'attendance_db',
            'user': 'root',
            'password': 'your_password',
            'charset': 'utf8mb4',
            'use_unicode': True
        }
        logger.warning("未找到config.py，使用默认数据库配置")
    
    excel_file = '8月.xlsx'
    
    # 创建导入器
    importer = SmartAttendanceImporter(db_config)
    
    try:
        # 连接数据库
        if not importer.connect_to_database():
            return
        
        # 创建表
        if not importer.create_attendance_table():
            return
        
        # 分析Excel结构
        df, structure = importer.analyze_excel_structure(excel_file)
        if df is None:
            return
        
        # 智能处理数据
        records = importer.process_data_smart(df, structure)
        if not records:
            logger.error("没有有效数据")
            return
        
        # 插入数据
        if importer.insert_data_batch(records):
            logger.info("数据导入成功完成！")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    
    finally:
        importer.close_connection()

if __name__ == "__main__":
    main()
