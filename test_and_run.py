#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和运行脚本
提供完整的测试、预览和导入功能
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_dependencies():
    """测试依赖包是否安装"""
    logger.info("检查依赖包...")
    
    required_packages = {
        'pandas': 'pandas',
        'mysql.connector': 'mysql-connector-python',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            logger.info(f"✓ {package} 已安装")
        except ImportError:
            logger.error(f"✗ {package} 未安装")
            missing_packages.append(pip_name)
    
    if missing_packages:
        logger.error(f"请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    logger.info("所有依赖包检查通过")
    return True

def test_excel_file(excel_file='8月.xlsx'):
    """测试Excel文件"""
    logger.info(f"检查Excel文件: {excel_file}")
    
    if not os.path.exists(excel_file):
        logger.error(f"Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 尝试读取文件
        df = pd.read_excel(excel_file)
        logger.info(f"✓ Excel文件读取成功，数据形状: {df.shape}")
        
        # 显示基本信息
        logger.info(f"列名: {df.columns.tolist()}")
        logger.info("前3行数据:")
        print(df.head(3).to_string())
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Excel文件读取失败: {e}")
        return False

def test_database_connection(db_config=None):
    """测试数据库连接"""
    if db_config is None:
        try:
            from config import DATABASE_CONFIG
            db_config = DATABASE_CONFIG
        except ImportError:
            logger.error("未找到config.py文件，请先配置数据库连接")
            return False
    
    logger.info("测试数据库连接...")
    
    try:
        connection = mysql.connector.connect(**db_config)
        if connection.is_connected():
            logger.info("✓ 数据库连接成功")
            
            # 测试数据库和表
            cursor = connection.cursor()
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()[0]
            logger.info(f"当前数据库: {db_name}")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'attendance_records'")
            table_exists = cursor.fetchone() is not None
            
            if table_exists:
                cursor.execute("SELECT COUNT(*) FROM attendance_records")
                count = cursor.fetchone()[0]
                logger.info(f"attendance_records表已存在，当前记录数: {count}")
            else:
                logger.info("attendance_records表不存在，将在导入时创建")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        logger.error(f"✗ 数据库连接失败: {e}")
        return False

def preview_import_data(excel_file='8月.xlsx', limit=10):
    """预览将要导入的数据"""
    logger.info(f"预览导入数据 (前{limit}条)...")
    
    try:
        from smart_attendance_importer import SmartAttendanceImporter
        
        # 创建临时导入器用于分析
        importer = SmartAttendanceImporter({})
        
        # 分析Excel结构
        df, structure = importer.analyze_excel_structure(excel_file)
        if df is None:
            return False
        
        # 处理数据
        records = importer.process_data_smart(df, structure)
        
        if not records:
            logger.error("没有找到有效数据")
            return False
        
        logger.info(f"总共将导入 {len(records)} 条记录")
        logger.info(f"预览前 {min(limit, len(records))} 条:")
        
        print("\n" + "="*80)
        print(f"{'序号':<4} {'员工姓名':<15} {'日期':<12} {'考勤类型':<20}")
        print("="*80)
        
        for i, record in enumerate(records[:limit], 1):
            print(f"{i:<4} {record['employee_name']:<15} {record['date']:<12} {record['attendance_type']:<20}")
        
        if len(records) > limit:
            print(f"... 还有 {len(records) - limit} 条记录")
        
        print("="*80)
        
        return True
        
    except Exception as e:
        logger.error(f"预览数据失败: {e}")
        return False

def run_import():
    """执行数据导入"""
    logger.info("开始执行数据导入...")
    
    try:
        from smart_attendance_importer import main as import_main
        import_main()
        return True
    except Exception as e:
        logger.error(f"导入失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("考勤数据导入工具 - 测试和运行")
    print("="*60)
    
    # 步骤1: 检查依赖
    if not test_dependencies():
        print("\n请先安装缺失的依赖包，然后重新运行")
        return
    
    # 步骤2: 检查Excel文件
    if not test_excel_file():
        print("\n请检查Excel文件是否存在且格式正确")
        return
    
    # 步骤3: 检查数据库连接
    if not test_database_connection():
        print("\n请检查数据库配置和连接")
        return
    
    # 步骤4: 预览数据
    print("\n" + "-"*60)
    if not preview_import_data():
        print("\n数据预览失败")
        return
    
    # 步骤5: 确认导入
    print("\n" + "-"*60)
    choice = input("是否继续导入数据到数据库? (y/n): ").strip().lower()
    
    if choice == 'y':
        print("\n开始导入数据...")
        if run_import():
            print("\n✓ 数据导入完成！")
        else:
            print("\n✗ 数据导入失败，请查看日志")
    else:
        print("\n已取消导入")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
